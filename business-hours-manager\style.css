.bd-container {
  width: 100%;
  max-width: 816px;
  margin: 0rem auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
  position: relative;
  background: var(--color-white);
}

.requires-confirmation {
  max-width: 628px;
}

.bd-float-left-icon {
  background: #c1c8e6;
  border-radius: 400px 400px 0 0;
  height: 350px;
  left: -350px;
  opacity: 0.2;
  position: absolute;
  top: 130px;
  width: 700px;
  z-index: 0;
}

.iti__selected-country {
  height: auto !important;
  top: 10px !important;
}

.iti__selected-dial-code {
  color: var(--color-gray-6) !important;
}

.bd-float-right-icon {
  position: absolute;
  top: auto;
  bottom: 148px;
  right: 0;
  width: 350px;
  height: 350px;
  background: #f84642;
  opacity: 0.1;
  border-radius: 350px 0 0 0;
  z-index: 0;
}

.step-validation-error {
  color: var(--color-primary);
  background-color: var(--color-primary-light);
  border: 1px solid var(--color-primary);
  padding: 16px;
  border-radius: 6px;
  margin: 24px 0;
  font-size: 14px;
}

/* Stepper styles */
.stepper-container-inner {
  width: 90%;
  margin: auto;
}

.bd-step {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  padding: 0;
  margin: 0 auto;
}

.bd-step-indicator {
  position: relative;
  display: flex;
  flex-direction: row;
  gap: 8px;
  color: var(--color-gray-5);
  font-size: 12px;
  align-items: center;
  text-decoration: none;
  cursor: default;
  user-select: none;
  z-index: 2;
  flex: 1;
  max-width: 200px;
  padding-bottom: 12px;
}

.bd-step-indicator::before {
  content: attr(data-step);
  width: 17px;
  height: 17px;
  border-radius: 50%;
  background: var(--color-gray-4);
  color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 400;
  transition: all 0.3s ease;
}

.bd-step-indicator::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: var(--color-gray-3);
  border-radius: 28px;
}

.bd-step-indicator span {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-gray-6);
  text-align: center;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.bd-step-indicator.active span {
  color: var(--color-secondary);
}

.bd-step-indicator.active::before,
.bd-step-indicator.active::after {
  background: var(--color-secondary);
}

/* Step content styles */
.bd-step-content {
  background: var(--color-white);
  border-radius: 1rem;
  border: 1px solid var(--color-gray-2);
  padding: 28px;
}

/*  Form Field Styles */
/* Form Row Layout */
.bd-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

/* Form Fields */
.bd-input-field {
  display: flex;
  flex-direction: column;
}

.bd-input-field label {
  color: var(--color-secondary);
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.bd-input-field input,
.bd-input-field select,
.custom-select-trigger {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--color-gray-2);
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--color-white);
}

.bd-input-field input:focus,
.bd-input-field select:focus,
.custom-select-trigger:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(210, 39, 37, 0.1);
}

.bd-input-field input:disabled,
.bd-input-field select:disabled {
  background: var(--color-gray-1);
  cursor: not-allowed;
}

.bd-input-field input::placeholder {
  color: var(--color-gray-5);
}

.bd-input-field select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.25rem;
  padding-right: 3rem;
}

.bd-button-group {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 24px;
}

.bd-hours-row {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  gap: 1rem;
  opacity: 1;
  overflow: hidden;
}

.bd-hours-row label {
  min-width: 32px;
  font-weight: 600;
  color: var(--color-secondary);
  font-size: 0.875rem;
  margin: 0;
}

/* Toggle Switch Styles */
.toggle-switch-container {
  display: flex;
  justify-content: left;
  align-items: center;
  flex: none;
  align-self: flex-start;
  min-width: 118px;
  min-height: 48px;
}

.toggle-switch {
  position: relative;
  display: flex;
  align-items: center;
  min-width: 58px;
}

.toggle-switch input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 20px;
  background-color: #ccc;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-right: 10px;
}

.toggle-slider:before {
  content: "";
  position: absolute;
  height: 16px;
  width: 16px;
  left: 2px;
  top: 2px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Checked state */
.toggle-switch input[type="checkbox"]:checked + .toggle-slider {
  background: var(--gradient-primary);
}

.toggle-switch input[type="checkbox"]:checked + .toggle-slider:before {
  transform: translateX(12px);
}

.unavailable-text {
  color: var(--color-gray-5);
  font-size: 0.875rem;
  font-style: italic;
  margin-left: 8px;
}

/* Time Input Styles */
.time-inputs-container,
.additional-time-slot {
  display: flex;
  gap: 8px;
  width: 100%;
  min-height: 48px;
  justify-content: left;
  align-items: center;
}

.time-inputs-container {
  flex: 1;
  flex-direction: column;
}

.time-inputs,
.time-inputs-default {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  width: 100%;
}

.time-select {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid var(--color-gray-2);
  border-radius: 6px;
  font-size: 1rem;
  background: var(--color-white);
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1rem;
  padding-right: 2rem;
  transition: all 0.3s ease;
}

.time-select:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(210, 39, 37, 0.1);
  outline: none;
}

.time-inputs span {
  color: var(--color-gray-6);
  font-weight: 700;
}

/* Add Hours Button */
.add-hours-btn {
  user-select: none;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background: var(--color-primary-light);
  position: relative;
  display: inline-block;
  flex-shrink: 0;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-hours-btn::after {
  content: "";
  position: absolute;
  height: 2px;
  width: 16px;
  background: var(--gradient-primary);
}

/* vertical line for plus only */
.plus-icon::before {
  content: "";
  position: absolute;
  width: 2px;
  height: 16px;
  background: var(--gradient-primary);
}

/* Scanner Hours Container */
.scanner-hours-container {
  margin-bottom: 1.5rem;
}

/* Scanner hours section with smooth animation */
#scanner-hours {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  overflow: hidden;
  min-height: 100%;
  transform: translateY(0);
}

#scanner-hours.hiding {
  opacity: 0;
  max-height: 0;
}

#scanner-hours.showing {
  opacity: 1;
  transform: translateY(0);
  margin-top: 12px;
}

/* When checkbox is checked, hide the scanner hours */
.scanner-hours-container.sync-enabled #scanner-hours {
  opacity: 0;
  max-height: 0;
}

/* Label base */ /* Hide native checkbox */
.scanner-toggle input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

/* Label styling */
.scanner-toggle label {
  position: relative;
  padding-left: 1.8rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  color: var(--color-secondary);
  margin-bottom: 0 !important;
}

/* Custom box */
.scanner-toggle label::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  border: 1px solid #c8277d;
  border-radius: 4px;
  background: #fff;
  transition: background 0.3s, box-shadow 0.3s;
}

/* Checkmark */
.scanner-toggle label::after {
  content: "";
  position: absolute;
  left: 6px;
  top: 50%;
  width: 5px;
  height: 10px;
  border-right: 2px solid #d22725;
  border-bottom: 2px solid #d22725;
  transform: translateY(-60%) rotate(45deg);
  opacity: 0;
  transition: opacity 0.3s;
}

/* When input is checked, show checkmark on label */
.scanner-toggle label.checked::after {
  opacity: 1 !important;
}

/* Make sure the form is responsive */
@media (max-width: 768px) {
  .bd-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .bd-hours-row {
    flex-wrap: wrap;
  }

  .time-inputs {
    width: 100%;
    margin-top: 0.5rem;
  }

  .add-hours-btn {
    margin-top: 0.5rem;
  }
}

.validation-error {
  color: var(--color-primary);
  font-size: 14px;
  margin-top: 4px;
}
