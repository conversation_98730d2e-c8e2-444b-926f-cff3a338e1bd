<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Custom Select Components</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="./main.css" />
  </head>
  <body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto space-y-8">
      <h1 class="text-3xl font-bold text-gray-800 mb-8">
        Custom Select Components
      </h1>

      <!-- New Features Showcase -->
      <div class="bg-blue-50 border border-blue-200 p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold text-blue-800 mb-4">
          ✨ Enhanced User Experience Features
        </h2>
        <div class="grid md:grid-cols-2 gap-4 text-sm text-blue-700">
          <div class="space-y-2">
            <div class="flex items-start gap-2">
              <span class="text-green-600 font-bold">✓</span>
              <span
                ><strong>Only One Dropdown Open:</strong> Opening a new dropdown
                automatically closes others, preventing confusion</span
              >
            </div>
            <div class="flex items-start gap-2">
              <span class="text-green-600 font-bold">✓</span>
              <span
                ><strong>Auto-scroll to Selected:</strong> Selected options are
                immediately visible when opening dropdowns</span
              >
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex items-start gap-2">
              <span class="text-green-600 font-bold">✓</span>
              <span
                ><strong>Memory Efficient:</strong> Automatic cleanup prevents
                memory leaks and handles DOM changes</span
              >
            </div>
            <div class="flex items-start gap-2">
              <span class="text-green-600 font-bold">✓</span>
              <span
                ><strong>Consistent Behavior:</strong> Works the same way across
                all dropdown instances</span
              >
            </div>
          </div>
        </div>
        <div class="mt-4 p-3 bg-blue-100 rounded border-l-4 border-blue-400">
          <p class="text-blue-800 text-sm">
            <strong>Try it:</strong> Open multiple dropdowns below to see how
            only one stays open at a time. Notice how selected options are
            automatically scrolled into view!
          </p>
        </div>
      </div>

      <!-- Multi-Select Examples -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-6">Multi-Select Dropdown</h2>

        <!-- Example 1: Basic Multi-Select -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2"
            >Service/Product Offered</label
          >
          <select
            id="multi-select-1"
            multiple
            class="hidden"
            data-select-type="multi"
            data-placeholder="Select services..."
          >
            <option value="health">Health Screening</option>
            <option value="opt1" selected>Option 1</option>
            <option value="opt2" selected>Option 2</option>
            <option value="opt3">Option 3</option>
            <option value="opt4">Option 4</option>
            <option value="opt5">Option 5</option>
            <option value="opt6">Option 6</option>
            <option value="opt7">Option 7</option>
            <option value="opt8">Option 8</option>
            <option value="opt9">Option 9</option>
            <option value="opt10">Option 10</option>
          </select>
        </div>
      </div>

      <!-- Single-Select Examples -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-6">Single-Select Dropdown</h2>

        <!-- Example 1: Basic Single-Select -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2"
            >Select Product</label
          >
          <select
            id="single-select-1"
            class="hidden"
            data-select-type="single"
            data-placeholder="Choose a product"
          >
            <option value="">Choose a product</option>
            <option value="health">Health Screening</option>
            <option value="opt1">Option 1</option>
            <option value="opt2">Option 2</option>
            <option value="opt3">Option 3</option>
            <option value="opt4">Option 4</option>
            <option value="opt5">Option 5</option>
            <option value="opt6">Option 6</option>
            <option value="opt7">Option 7</option>
            <option value="opt8">Option 8</option>
            <option value="opt9">Option 9</option>
            <option value="opt10">Option 10</option>
            <option value="opt11">Option 11</option>
            <option value="opt12">Option 12</option>
            <option value="opt13">Option 13</option>
            <option value="opt14">Option 14</option>
            <option value="opt15" selected>Option 15</option>
            <option value="opt16">Option 16</option>
            <option value="opt17">Option 17</option>
            <option value="opt18">Option 18</option>
            <option value="opt19">Option 19</option>
            <option value="opt20">Option 20</option>
          </select>
        </div>

        <!-- Test Multiple Dropdowns -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2"
            >Test Multiple Dropdowns (Option 10 selected)</label
          >
          <select
            id="single-select-2"
            class="hidden"
            data-select-type="single"
            data-placeholder="Choose another option"
          >
            <option value="">Choose another option</option>
            <option value="test1">Test Option 1</option>
            <option value="test2">Test Option 2</option>
            <option value="test3">Test Option 3</option>
            <option value="test4">Test Option 4</option>
            <option value="test5">Test Option 5</option>
            <option value="test6">Test Option 6</option>
            <option value="test7">Test Option 7</option>
            <option value="test8">Test Option 8</option>
            <option value="test9">Test Option 9</option>
            <option value="test10" selected>Test Option 10</option>
            <option value="test11">Test Option 11</option>
            <option value="test12">Test Option 12</option>
          </select>
        </div>
      </div>

      <!-- Form Integration Example -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-6">Form Integration Example</h2>
        <form id="demo-form" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Department</label
              >
              <select
                id="department-select"
                class="hidden"
                data-select-type="single"
                data-placeholder="Select Department"
              >
                <option value="">Select Department</option>
                <option value="hr">Human Resources</option>
                <option value="it">Information Technology</option>
                <option value="finance">Finance</option>
                <option value="marketing">Marketing</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Skills</label
              >
              <select
                id="skills-select"
                multiple
                class="hidden"
                data-select-type="multi"
                data-placeholder="Select your skills..."
              >
                <option value="js">JavaScript</option>
                <option value="python">Python</option>
                <option value="react">React</option>
                <option value="node">Node.js</option>
                <option value="sql">SQL</option>
              </select>
            </div>
          </div>
          <button
            type="submit"
            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Submit Form
          </button>
        </form>
      </div>

      <!-- Enhanced UX Demonstration -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">🎯 Enhanced UX Demonstration</h2>
        <p class="text-gray-600 mb-6">
          Try opening multiple dropdowns below to see the improved behavior in
          action:
        </p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Demo Dropdown 1 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Demo Multi-Select 1
            </label>
            <select
              id="demo-multi-1"
              multiple
              class="hidden"
              data-select-type="multi"
              data-placeholder="Select items..."
            >
              <option value="item1">Item 1</option>
              <option value="item2" selected>Item 2 (Selected)</option>
              <option value="item3">Item 3</option>
              <option value="item4">Item 4</option>
              <option value="item5" selected>Item 5 (Selected)</option>
              <option value="item6">Item 6</option>
              <option value="item7">Item 7</option>
              <option value="item8">Item 8</option>
              <option value="item9">Item 9</option>
              <option value="item10">Item 10</option>
            </select>
          </div>

          <!-- Demo Dropdown 2 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Demo Single-Select
            </label>
            <select
              id="demo-single-1"
              class="hidden"
              data-select-type="single"
              data-placeholder="Choose option..."
            >
              <option value="">Choose option...</option>
              <option value="alpha">Alpha</option>
              <option value="beta">Beta</option>
              <option value="gamma">Gamma</option>
              <option value="delta">Delta</option>
              <option value="epsilon">Epsilon</option>
              <option value="zeta">Zeta</option>
              <option value="eta">Eta</option>
              <option value="theta" selected>Theta (Selected)</option>
              <option value="iota">Iota</option>
              <option value="kappa">Kappa</option>
            </select>
          </div>

          <!-- Demo Dropdown 3 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Demo Multi-Select 2
            </label>
            <select
              id="demo-multi-2"
              multiple
              class="hidden"
              data-select-type="multi"
              data-placeholder="Pick options..."
            >
              <option value="red">Red</option>
              <option value="orange">Orange</option>
              <option value="yellow">Yellow</option>
              <option value="green" selected>Green (Selected)</option>
              <option value="blue">Blue</option>
              <option value="indigo">Indigo</option>
              <option value="violet" selected>Violet (Selected)</option>
              <option value="pink">Pink</option>
              <option value="brown">Brown</option>
              <option value="black">Black</option>
            </select>
          </div>
        </div>

        <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
          <h3 class="font-semibold text-yellow-800 mb-2">What to Notice:</h3>
          <ul class="text-sm text-yellow-700 space-y-1">
            <li>
              • <strong>Global Management:</strong> Only one dropdown can be
              open at a time
            </li>
            <li>
              • <strong>Auto-scroll:</strong> Selected options are immediately
              visible when opening
            </li>
            <li>
              • <strong>Smooth Experience:</strong> No need to manually scroll
              to find your selections
            </li>
            <li>
              • <strong>Memory Efficient:</strong> Proper cleanup when dropdowns
              are closed or destroyed
            </li>
          </ul>
        </div>

        <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded">
          <h3 class="font-semibold text-green-800 mb-2">
            🔧 Keyboard Navigation Test:
          </h3>
          <ol class="text-sm text-green-700 space-y-1">
            <li>1. <strong>Open any multi-select dropdown above</strong></li>
            <li>
              2. <strong>Use ↑/↓ arrow keys</strong> - Notice the focus
              highlight (pink outline)
            </li>
            <li>
              3. <strong>Click on any option</strong> to select/deselect it
            </li>
            <li>
              4. <strong>Continue using ↑/↓ arrow keys</strong> - Navigation
              should still work!
            </li>
            <li>
              5. <strong>Press Enter/Space</strong> to toggle the focused option
            </li>
          </ol>
          <p class="text-xs text-green-600 mt-2">
            ✅ <strong>Fixed Issues:</strong> Focus highlighting now works
            properly, and keyboard navigation continues after clicking options.
          </p>
        </div>
      </div>

      <!-- Browser Navigation Prevention Demo -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">
          🛡️ Browser Navigation Prevention
        </h2>

        <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded">
          <h3 class="font-semibold text-blue-800 mb-2">How it Works:</h3>
          <ul class="text-sm text-blue-700 space-y-1">
            <li>
              • <strong>Automatic Protection:</strong> When you change any
              dropdown selection, navigation prevention is automatically enabled
            </li>
            <li>
              • <strong>Browser Warnings:</strong> Trying to refresh, close tab,
              or navigate away will show a confirmation dialog
            </li>
            <li>
              • <strong>Smart Recovery:</strong> If you cancel navigation,
              dropdowns continue working perfectly
            </li>
            <li>
              • <strong>Manual Control:</strong> You can enable/disable
              protection programmatically
            </li>
          </ul>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium mb-2"
              >Test Navigation Prevention</label
            >
            <select
              id="navigation-demo"
              data-select-type="multi"
              data-placeholder="Change selections to enable protection..."
              multiple
            >
              <option value="nav1">Navigation Test 1</option>
              <option value="nav2">Navigation Test 2</option>
              <option value="nav3">Navigation Test 3</option>
              <option value="nav4">Navigation Test 4</option>
              <option value="nav5">Navigation Test 5</option>
            </select>

            <div class="mt-4 space-y-2">
              <button
                onclick="CustomSelect.preventNavigation('Custom warning: You have unsaved dropdown changes!')"
                class="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
              >
                🔒 Enable Protection (Custom Message)
              </button>
              <button
                onclick="CustomSelect.allowNavigation()"
                class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
              >
                ✅ Allow Navigation (Disable Protection)
              </button>
            </div>
          </div>

          <div>
            <h3 class="text-sm font-medium mb-2">Test Instructions:</h3>
            <ol class="text-sm text-gray-700 space-y-2 mb-4">
              <li>
                1. <strong>Change dropdown selections</strong> - Protection
                auto-enables
              </li>
              <li>
                2. <strong>Try to refresh page (Ctrl+R/F5)</strong> - See
                warning dialog
              </li>
              <li>
                3. <strong>Try to close tab (Ctrl+W)</strong> - See warning
                dialog
              </li>
              <li>
                4. <strong>Click "Cancel"</strong> in dialog - Stay on page
              </li>
              <li>
                5. <strong>Test dropdowns still work</strong> - They should work
                perfectly!
              </li>
              <li>
                6. <strong>Click "Allow Navigation"</strong> - Disable
                protection
              </li>
            </ol>

            <div class="p-3 bg-gray-50 rounded border">
              <h4 class="text-xs font-semibold mb-2">Protection Status:</h4>
              <div id="protection-status" class="text-xs font-mono">
                <span class="text-gray-500">Not protected</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Event Handling Demo -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Event Handling Demo</h2>
        <div
          id="event-log"
          class="bg-gray-50 p-4 rounded border h-32 overflow-y-auto text-sm font-mono"
        >
          <div class="text-gray-500">Events will appear here...</div>
        </div>
      </div>
    </div>

    <script src="./js/custom-select.js"></script>
    <!-- <script src="./js/custom-select.min.js"></script> -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Selects are auto-initialized via data-select-type attribute
        // Get references to the select instances if needed
        const multiSelect1 = document.getElementById("multi-select-1");
        const skillsSelect = document.getElementById("skills-select");
        const singleSelect1 = document.getElementById("single-select-1");
        const departmentSelect = document.getElementById("department-select");
        const navigationDemo = document.getElementById("navigation-demo");

        const eventLog = document.getElementById("event-log");
        const protectionStatus = document.getElementById("protection-status");

        function logEvent(message) {
          const timestamp = new Date().toLocaleTimeString();
          const logEntry = document.createElement("div");
          logEntry.textContent = `[${timestamp}] ${message}`;
          eventLog.appendChild(logEntry);
          eventLog.scrollTop = eventLog.scrollHeight;
        }

        function updateProtectionStatus() {
          if (window.CustomSelectNavigationManager) {
            const isProtected =
              window.CustomSelectNavigationManager.isNavigationPrevented;
            const hasChanges =
              window.CustomSelectNavigationManager.hasUnsavedChanges;

            if (isProtected && hasChanges) {
              protectionStatus.innerHTML =
                '<span class="text-red-600">🔒 Protected - Navigation will show warning</span>';
            } else if (isProtected) {
              protectionStatus.innerHTML =
                '<span class="text-yellow-600">🔒 Protection enabled</span>';
            } else {
              protectionStatus.innerHTML =
                '<span class="text-gray-500">Not protected</span>';
            }
          }
        }

        // Update protection status every second
        setInterval(updateProtectionStatus, 1000);
        updateProtectionStatus();

        // Add event listeners for demonstration
        multiSelect1.addEventListener("change", (e) => {
          console.log(e.detail.selectedValues);
          logEvent(
            `Multi-select 1: ${e.detail.selectedValues.length} items selected`
          );
        });

        singleSelect1.addEventListener("change", (e) => {
          logEvent(
            `Single-select 1: ${
              e.detail.selectedOption?.label || "None"
            } selected`
          );
        });

        skillsSelect.addEventListener("change", (e) => {
          logEvent(
            `Skills: ${e.detail.selectedOptions.map((o) => o.label).join(", ")}`
          );
        });

        departmentSelect.addEventListener("change", (e) => {
          logEvent(
            `Department: ${e.detail.selectedOption?.label || "None"} selected`
          );
        });

        // Form submission demo
        document.getElementById("demo-form").addEventListener("submit", (e) => {
          e.preventDefault();
          const department = departmentSelect.value;
          const skills = Array.from(skillsSelect.selectedOptions).map(
            (opt) => opt.value
          );
          
          logEvent(
            `Form submitted - Department: ${department}, Skills: ${skills.join(
              ", "
            )}`
          );
        });
      });
    </script>
  </body>
</html>
